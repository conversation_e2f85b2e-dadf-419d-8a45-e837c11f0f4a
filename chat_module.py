"""
OpenAI Chat Integration Module for MIT CVD App
Handles text, image, and audio processing via OpenAI API
"""
import openai
import base64
import json
from typing import Dict, List, Optional, Any, Union
import os

from config import OPENAI_API_KEY, OPENAI_ORG_ID
from data_models import current_timestamp

# Initialize OpenAI client
if OPENAI_API_KEY:
    try:
        from openai import OpenAI
        client = OpenAI(api_key=OPENAI_API_KEY)
        OPENAI_CLIENT_AVAILABLE = True
    except ImportError:
        # Fallback for older versions
        openai.api_key = OPENAI_API_KEY
        if OPENAI_ORG_ID:
            openai.organization = OPENAI_ORG_ID
        OPENAI_CLIENT_AVAILABLE = False
else:
    OPENAI_CLIENT_AVAILABLE = False

class CVDChatAssistant:
    """CVD-focused chat assistant using OpenAI"""
    
    def __init__(self):
        self.system_prompt = """
        You are a cardiovascular health assistant for the MIT CVD App. Your role is to:
        
        1. Analyze user inputs (text, images of food, health data) for cardiovascular risk factors
        2. Provide personalized health insights and recommendations
        3. Extract actionable health metrics from conversations
        4. Focus on diet, exercise, stress, and lifestyle factors affecting heart health
        
        Always provide:
        - Clear, actionable health insights
        - Risk factor identification
        - Lifestyle recommendations
        - Encouragement for healthy behaviors
        
        Keep responses concise but informative. Always recommend consulting healthcare professionals for medical decisions.
        """
    
    def get_insights(self, input_data: Union[str, Dict[str, Any]], user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main function to get health insights from various input types
        
        Args:
            input_data: Text string, or dict with 'type' and 'content' keys
            user_context: User profile data for personalized insights
            
        Returns:
            Dict with response, insights, and extracted health metrics
        """
        try:
            if isinstance(input_data, str):
                return self._process_text_input(input_data, user_context)
            elif isinstance(input_data, dict):
                input_type = input_data.get('type', 'text')
                content = input_data.get('content', '')
                
                if input_type == 'text':
                    return self._process_text_input(content, user_context)
                elif input_type == 'image':
                    return self._process_image_input(content, user_context)
                elif input_type == 'audio_transcript':
                    return self._process_audio_transcript(content, user_context)
                else:
                    return self._create_error_response(f"Unsupported input type: {input_type}")
            else:
                return self._create_error_response("Invalid input format")
                
        except Exception as e:
            return self._create_error_response(f"Error processing input: {str(e)}")
    
    def _process_text_input(self, text: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process text input for health insights"""
        try:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": self._build_user_message(text, user_context)}
            ]
            
            if OPENAI_CLIENT_AVAILABLE:
                response = client.chat.completions.create(
                    model="gpt-4",
                    messages=messages,
                    max_tokens=500,
                    temperature=0.7
                )
                ai_response = response.choices[0].message.content
            else:
                # Fallback for older API
                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=messages,
                    max_tokens=500,
                    temperature=0.7
                )
                ai_response = response.choices[0].message.content

            
            # Extract insights and metrics
            insights = self._extract_insights(ai_response, text)
            
            return {
                'success': True,
                'response': ai_response,
                'insights': insights,
                'input_type': 'text',
                'timestamp': current_timestamp()
            }
            
        except Exception as e:
            return self._create_error_response(f"OpenAI API error: {str(e)}")
    
    def _process_image_input(self, image_path: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process image input (food analysis)"""
        try:
            if not os.path.exists(image_path):
                return self._create_error_response(f"Image file not found: {image_path}")
            
            # Encode image to base64
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Analyze this food image for cardiovascular health. Identify the food, estimate nutritional content, and provide heart-healthy recommendations."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ]
            
            if OPENAI_CLIENT_AVAILABLE:
                response = client.chat.completions.create(
                    model="gpt-4-vision-preview",
                    messages=messages,
                    max_tokens=400
                )
                ai_response = response.choices[0].message.content
            else:
                # Fallback for older API
                response = openai.ChatCompletion.create(
                    model="gpt-4-vision-preview",
                    messages=messages,
                    max_tokens=400
                )
                ai_response = response.choices[0].message.content

            insights = self._extract_food_insights(ai_response)
            
            return {
                'success': True,
                'response': ai_response,
                'insights': insights,
                'input_type': 'image',
                'image_path': image_path,
                'timestamp': current_timestamp()
            }
            
        except Exception as e:
            return self._create_error_response(f"Image processing error: {str(e)}")
    
    def _process_audio_transcript(self, transcript: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process audio transcript for health insights"""
        # Add audio-specific context
        enhanced_text = f"Audio transcript from user: {transcript}"
        return self._process_text_input(enhanced_text, user_context)
    
    def _build_user_message(self, text: str, user_context: Dict[str, Any] = None) -> str:
        """Build contextualized user message"""
        message = f"User input: {text}\n\n"
        
        if user_context:
            message += "User context:\n"
            if 'age' in user_context:
                message += f"- Age: {user_context['age']}\n"
            if 'profile' in user_context:
                profile = user_context['profile']
                if 'lifestyle' in profile:
                    lifestyle = profile['lifestyle']
                    message += f"- Exercise frequency: {lifestyle.get('exercise_frequency', 'unknown')} times/week\n"
                    message += f"- Smoking: {'Yes' if lifestyle.get('smoking') else 'No'}\n"
                    message += f"- Stress level: {lifestyle.get('stress_level', 'unknown')}/10\n"
        
        message += "\nProvide cardiovascular health insights and recommendations."
        return message
    
    def _extract_insights(self, ai_response: str, original_input: str) -> Dict[str, Any]:
        """Extract structured insights from AI response"""
        insights = {
            'risk_factors': [],
            'recommendations': [],
            'positive_behaviors': [],
            'metrics': {}
        }
        
        # Simple keyword-based extraction (can be enhanced with NLP)
        response_lower = ai_response.lower()
        
        # Risk factors
        risk_keywords = ['smoking', 'stress', 'sedentary', 'high sodium', 'processed food', 'alcohol']
        for keyword in risk_keywords:
            if keyword in response_lower:
                insights['risk_factors'].append(keyword)
        
        # Positive behaviors
        positive_keywords = ['exercise', 'vegetables', 'fruits', 'walking', 'meditation', 'sleep']
        for keyword in positive_keywords:
            if keyword in response_lower:
                insights['positive_behaviors'].append(keyword)
        
        return insights
    
    def _extract_food_insights(self, ai_response: str) -> Dict[str, Any]:
        """Extract food-specific insights"""
        insights = {
            'food_identified': True,
            'heart_healthy_score': 5,  # Default neutral score
            'nutritional_highlights': [],
            'recommendations': []
        }
        
        # Extract food name and nutritional info
        # This is a simplified version - could be enhanced with structured prompts
        response_lower = ai_response.lower()
        
        # Heart-healthy indicators
        if any(word in response_lower for word in ['omega-3', 'fiber', 'antioxidants', 'vegetables']):
            insights['heart_healthy_score'] = 8
        elif any(word in response_lower for word in ['fried', 'processed', 'high sodium', 'saturated fat']):
            insights['heart_healthy_score'] = 3
        
        return insights
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            'success': False,
            'error': error_message,
            'response': "I'm sorry, I couldn't process your request. Please try again.",
            'insights': {},
            'timestamp': current_timestamp()
        }

# Convenience functions
def get_insights(input_data: Union[str, Dict[str, Any]], user_context: Dict[str, Any] = None) -> Dict[str, Any]:
    """Main function to get insights - wrapper for the class method"""
    assistant = CVDChatAssistant()
    return assistant.get_insights(input_data, user_context)

def quick_health_check(symptoms_or_concerns: str, user_age: int = None) -> Dict[str, Any]:
    """Quick health check for specific symptoms or concerns"""
    user_context = {'age': user_age} if user_age else None
    prompt = f"Health concern: {symptoms_or_concerns}. Please provide cardiovascular risk assessment and recommendations."
    return get_insights(prompt, user_context)
