# 🏥 MIT CVD App - Cardiovascular Health Assessment Platform

A comprehensive, modular Python application for cardiovascular disease (CVD) risk assessment, built for hackathon demonstration. This MVP integrates AI-powered health conversations, food analysis, risk calculations, gamification, and PDF reporting.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- OpenAI API key (optional for demo mode)

### Installation

1. **Run setup script**
   ```bash
   python setup.py
   ```

2. **Configure API key** (optional)
   ```bash
   # Edit .env file and add your OpenAI API key
   OPENAI_API_KEY=your_api_key_here
   ```

### Running the App

**Demo Mode (3-minute demonstration)**
```bash
python main.py demo
```

**Interactive Mode**
```bash
python main.py
```

## 🏥 Features

### Core Modules
- **👤 User Management**: JSON-based user profiles with lifestyle tracking
- **💬 AI Health Chat**: OpenAI-powered health conversations and insights
- **📸 Food Analysis**: Computer vision for nutritional assessment
- **🎤 Audio Processing**: Whisper API for voice-to-text health inputs
- **📊 CVD Risk Scoring**: Comprehensive cardiovascular risk assessment
- **🧠 Advanced Heuristics**: Multi-factor health pattern analysis
- **🎮 Gamification**: Streak tracking, achievements, and point systems
- **📄 PDF Reports**: Comprehensive health report generation

## 🎯 3-Minute Demo Flow

The demo showcases:

1. **User Setup**: Create demo user with health profile
2. **AI Conversation**: Health chat with risk factor analysis
3. **Food Analysis**: Mock food image processing
4. **CVD Assessment**: Comprehensive risk calculation
5. **Gamification**: Streak updates and achievements
6. **PDF Export**: Generate health reports

## 📋 Module Architecture

### 1. CRUD Operations (`crud_module.py`)
- JSON-based local database
- User profile management
- Session and streak tracking

### 2. AI Chat Integration (`chat_module.py`)
- OpenAI GPT-4 integration
- Health-focused conversation analysis
- Risk factor identification

### 3. Audio Processing (`audio_module.py`)
- Whisper API transcription
- Health keyword detection
- Format conversion support

### 4. Computer Vision (`vision_module.py`)
- Food image analysis
- Nutritional content estimation
- Heart-healthy scoring

### 5. CVD Risk Calculation (`cvd_score_module.py`)
- Multi-factor risk assessment
- Age, lifestyle, and family history analysis
- Trend tracking over time

### 6. Advanced Heuristics (`heuristic_module.py`)
- Behavioral pattern analysis
- Consistency scoring
- Environmental factor assessment

### 7. Gamification (`gamification_module.py`)
- Daily streak tracking
- Achievement system
- Point-based rewards

### 8. PDF Export (`pdf_module.py`)
- Professional report generation
- Multiple report types
- Charts and visualizations

## 🔧 Configuration

### Environment Variables (.env)
```bash
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# App Configuration
APP_NAME=MIT_CVD_APP
DEBUG=True
```

## 🧪 Testing

Test the installation:
```bash
python -c "import crud_module; print('✅ Modules working')"
```

Run demo mode:
```bash
python main.py demo
```

## 📈 Hackathon Demo Script

**Live Demo Flow (3 minutes):**

1. **[30s] Setup**: Show modular architecture, create user
2. **[90s] Core Features**: AI chat, food analysis, CVD assessment
3. **[60s] Advanced**: Heuristics, gamification, PDF reports

## 🛠️ Development

### Adding New Modules
1. Create module file following naming convention
2. Implement required functions with consistent return formats
3. Add to main.py integration

### Module Template
```python
def main_function(input_data: Dict[str, Any]) -> Dict[str, Any]:
    try:
        result = process_data(input_data)
        return {
            'success': True,
            'data': result,
            'timestamp': current_timestamp()
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }
```

---

**Built for MIT Hackathon 2024** 🏆

> ⚠️ **Atenção:** este código é apenas um **mockup funcional** para hackathon.
> Não possui preocupações de segurança, escalabilidade ou persistência avançada.
> O banco de dados é um **arquivo JSON local**.

---

## 🚀 Funcionalidades do Backend

### 1. Conversação (OpenAI API) – `chat_module.py`

* Recebe **texto, imagem ou áudio** como entrada.
* Retorna **JSON** com resposta + insights gerados pela API da OpenAI.

### 2. CRUD de Clientes (JSON DB) – `crud_module.py`

* Banco de dados mockado em JSON.
* Funções: **criar, ler, atualizar e deletar usuários**.
* Cada usuário possui um **array de scores**.
* Cálculo de **média de scores** embutido.

### 3. Exportação em PDF – `pdf_module.py`

* Geração de **relatórios em PDF** contendo:

  * Dados do usuário.
  * Histórico de hábitos.
  * Score médio.
  * Insights de risco cardiovascular.

### 4. Áudio para Texto (Whisper API) – `audio_module.py`

* Converte arquivos de áudio em **transcrição textual**.
* Integra com a API Whisper.

### 5. Visão Computacional para Alimentos – `vision_module.py`

* Recebe uma **imagem de alimento**.
* Retorna o **nome do alimento** e uma **tabela nutricional mockada**.

### 6. Heurística Ponderada Personalizada – `heuristic_module.py`

* Combina informações de:

  * **Histórico familiar**.
  * **Hábitos reportados manualmente**.
  * **Alimentação detectada/reportada**.
* Retorna um **score personalizado de risco**.

### 7. Score de Risco CVD (beta) – `cvd_score_module.py`

* Implementa uma versão **simplificada** de calculadoras reconhecidas de risco cardiovascular.
* Output: **score numérico** + categoria de risco (baixo/médio/alto).

### 8. Gamificação (Streak de Adesão) – `gamification_module.py`

* Implementa **check-in diário simples**.
* Contador de **streak** salvo no JSON do usuário.

---

## 📂 Estrutura de Módulos

```
/backend
│── chat_module.py
│── crud_module.py
│── pdf_module.py
│── audio_module.py
│── vision_module.py
│── heuristic_module.py
│── cvd_score_module.py
│── gamification_module.py
│── database.json   # mockup de dados
```

---

## 🛠️ Stack Técnica

* **Linguagem:** Python 3.x
* **Banco:** JSON local (mock)
* **APIs externas:**

  * OpenAI (Chat & Whisper)
* **Bibliotecas sugeridas:**

  * `requests`
  * `fpdf` ou `reportlab` (para PDF)
  * `json`, `datetime` (nativos)

---

## 🧪 Status

✅ MVP funcional para **demo ao vivo**.
❌ Não pronto para produção (sem segurança, sem autenticação, sem escalabilidade).

---

👉 Quer que eu adicione no README também um **roteiro de como rodar a demo (exemplo de chamadas Python para cada módulo)** ou deixa só como descrição das features?
