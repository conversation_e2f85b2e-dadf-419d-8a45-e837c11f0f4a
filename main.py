"""
MIT CVD App - Main Application
Integrates all modules for a comprehensive cardiovascular health assessment platform
"""
import os
import sys
from typing import Dict, Any, Optional
from datetime import datetime

# Import all modules
import crud_module
import chat_module
import audio_module
import vision_module
import cvd_score_module
import heuristic_module
import gamification_module
import pdf_module
from config import OPENAI_API_KEY

class CVDApp:
    """Main application class integrating all CVD modules"""
    
    def __init__(self):
        self.current_user = None
        self.session_data = {}
        
    def run_demo(self):
        """Run the 3-minute demo showcasing all features"""
        print("🏥 MIT CVD App - 3-Minute Demo")
        print("=" * 50)
        
        # Demo flow
        self._demo_user_setup()
        self._demo_health_chat()
        self._demo_food_analysis()
        self._demo_cvd_assessment()
        self._demo_gamification()
        self._demo_pdf_export()
        
        print("\n🎉 Demo completed! All modules working successfully.")
        print("Ready for live demonstration.")
    
    def run_interactive(self):
        """Run interactive mode for full app experience"""
        print("🏥 MIT CVD App - Interactive Mode")
        print("=" * 40)
        
        while True:
            self._show_main_menu()
            choice = input("\nEnter your choice (1-8, or 'q' to quit): ").strip()
            
            if choice.lower() == 'q':
                print("Thank you for using MIT CVD App!")
                break
            
            try:
                choice_num = int(choice)
                self._handle_menu_choice(choice_num)
            except ValueError:
                print("Invalid choice. Please enter a number 1-8 or 'q' to quit.")
            
            input("\nPress Enter to continue...")
    
    def _demo_user_setup(self):
        """Demo: User creation and profile setup"""
        print("\n1️⃣ User Setup & Profile Creation")
        print("-" * 30)
        
        # Create demo user
        demo_user = crud_module.create_user(
            name="Alex Johnson",
            age=45,
            email="<EMAIL>"
        )
        
        if demo_user:
            self.current_user = demo_user
            print(f"✅ Created user: {demo_user['name']}, Age: {demo_user['age']}")
            
            # Update with lifestyle data
            lifestyle_update = {
                'profile': {
                    'lifestyle': {
                        'smoking': False,
                        'exercise_frequency': 3,
                        'stress_level': 6,
                        'sleep_hours': 7
                    },
                    'family_history': {
                        'heart_attack': True,
                        'high_blood_pressure': True
                    }
                }
            }
            
            crud_module.update_user(demo_user['id'], lifestyle_update)
            print("✅ Updated lifestyle and family history data")
        else:
            print("❌ Failed to create demo user")
    
    def _demo_health_chat(self):
        """Demo: Health conversation with AI"""
        print("\n2️⃣ AI Health Conversation")
        print("-" * 30)
        
        if not OPENAI_API_KEY:
            print("⚠️ OpenAI API key not configured - using mock response")
            response = {
                'success': True,
                'response': "Based on your family history and current lifestyle, I recommend focusing on regular exercise and stress management. Your 3x weekly exercise routine is excellent! Consider adding meditation for stress reduction.",
                'insights': {
                    'risk_factors': ['family history', 'moderate stress'],
                    'positive_behaviors': ['regular exercise', 'non-smoker']
                }
            }
        else:
            # Real AI conversation
            user_input = "I've been feeling some chest tightness after exercise. My family has a history of heart disease."
            response = chat_module.get_insights(user_input, self.current_user)
        
        if response['success']:
            print(f"💬 User: Family history concerns and chest tightness")
            print(f"🤖 AI: {response['response'][:150]}...")
            print(f"📊 Insights: {len(response.get('insights', {}).get('risk_factors', []))} risk factors identified")
        else:
            print(f"❌ Chat error: {response.get('error', 'Unknown error')}")
    
    def _demo_food_analysis(self):
        """Demo: Food image analysis"""
        print("\n3️⃣ Food Image Analysis")
        print("-" * 30)
        
        # Use mock analysis for demo (since we don't have actual food images)
        print("📸 Analyzing food image: 'grilled_salmon_vegetables.jpg'")
        
        if not OPENAI_API_KEY:
            print("⚠️ Using mock food analysis")
            analysis = vision_module.mock_analyze_food("demo_food.jpg")
        else:
            # Would use real image analysis
            analysis = vision_module.mock_analyze_food("demo_food.jpg")
        
        if analysis['success']:
            print(f"🍽️ Food identified: {analysis['food_name']}")
            print(f"❤️ Heart health score: {analysis['heart_health_score']}/10")
            print(f"⚠️ CVD risk level: {analysis['cvd_impact']['risk_level']}")
            
            # Log nutrition data
            if self.current_user:
                nutrition_log = {
                    'name': analysis['food_name'],
                    'nutrition': analysis['nutrition'],
                    'image_path': analysis['image_path']
                }
                crud_module.add_nutrition_log_to_user(self.current_user['id'], nutrition_log)
                print("✅ Nutrition data logged to user profile")
        else:
            print(f"❌ Food analysis error: {analysis.get('error', 'Unknown error')}")
    
    def _demo_cvd_assessment(self):
        """Demo: CVD risk calculation"""
        print("\n4️⃣ CVD Risk Assessment")
        print("-" * 30)
        
        if not self.current_user:
            print("❌ No user data available for assessment")
            return
        
        # Get updated user data
        user_data = crud_module.read_user(self.current_user['id'])
        
        # Calculate CVD risk
        cvd_result = cvd_score_module.calculate_cvd_risk(user_data)
        
        if cvd_result['success']:
            print(f"📊 CVD Risk Score: {cvd_result['percentage']}%")
            print(f"⚠️ Risk Level: {cvd_result['risk_level']}")
            print(f"🔍 Risk Factors: {len(cvd_result['risk_factors'])} identified")
            print(f"🛡️ Protective Factors: {len(cvd_result['protective_factors'])} identified")
            
            # Save CVD score
            crud_module.add_cvd_score_to_user(self.current_user['id'], cvd_result['score_data'])
            print("✅ CVD assessment saved to user profile")
            
            # Calculate heuristic score
            heuristic_result = heuristic_module.calculate_heuristic(user_data)
            if heuristic_result['success']:
                print(f"🧠 Heuristic Score: {heuristic_result['heuristic_score']:.3f}")
                print(f"📈 Risk Category: {heuristic_result['risk_category']}")
        else:
            print(f"❌ CVD assessment error: {cvd_result.get('error', 'Unknown error')}")
    
    def _demo_gamification(self):
        """Demo: Gamification and streak tracking"""
        print("\n5️⃣ Gamification & Streaks")
        print("-" * 30)
        
        if not self.current_user:
            print("❌ No user data available for gamification")
            return
        
        # Update streak for daily check-in
        streak_result = gamification_module.update_streak(self.current_user['id'], 'daily_checkin')
        
        if streak_result['success']:
            streak_info = streak_result['streak_info']
            print(f"🔥 Current Streak: {streak_info['current_streak']} days")
            print(f"🏆 Longest Streak: {streak_info['longest_streak']} days")
            print(f"⭐ Total Points: {streak_info['total_points']}")
            print(f"🎯 Points Earned: +{streak_result['points_earned']}")
            
            # Check for new achievements
            new_achievements = streak_result['new_achievements']
            if new_achievements:
                print(f"🏅 New Achievements: {len(new_achievements)}")
                for achievement in new_achievements:
                    print(f"   {achievement['icon']} {achievement['name']}")
            
            # Log additional activities
            gamification_module.log_activity(self.current_user['id'], 'nutrition_log')
            gamification_module.log_activity(self.current_user['id'], 'cvd_assessment')
            print("✅ Additional activities logged for points")
        else:
            print(f"❌ Gamification error: {streak_result.get('error', 'Unknown error')}")
    
    def _demo_pdf_export(self):
        """Demo: PDF report generation"""
        print("\n6️⃣ PDF Report Generation")
        print("-" * 30)
        
        if not self.current_user:
            print("❌ No user data available for PDF export")
            return
        
        # Get complete user data
        user_data = crud_module.read_user(self.current_user['id'])
        
        # Generate comprehensive report
        pdf_result = pdf_module.export_user_report(user_data, 'comprehensive')
        
        if pdf_result['success']:
            print(f"📄 PDF Report Generated: {pdf_result['filename']}")
            print(f"📁 File Path: {pdf_result['filepath']}")
            print(f"📊 File Size: {pdf_result['file_size']} bytes")
            print(f"📅 Generated: {pdf_result['generated_at']}")
            
            # Generate summary report too
            summary_result = pdf_module.quick_summary_report(user_data)
            if summary_result['success']:
                print(f"📋 Summary Report: {summary_result['filename']}")
        else:
            print(f"❌ PDF generation error: {pdf_result.get('error', 'Unknown error')}")
    
    def _show_main_menu(self):
        """Display main menu options"""
        print("\n🏥 MIT CVD App - Main Menu")
        print("=" * 30)
        print("1. 👤 User Management")
        print("2. 💬 Health Chat")
        print("3. 📸 Food Analysis")
        print("4. 🎤 Audio Input")
        print("5. 📊 CVD Assessment")
        print("6. 🎮 View Progress & Streaks")
        print("7. 📄 Generate Report")
        print("8. 🔧 System Status")
        print("q. 🚪 Quit")
    
    def _handle_menu_choice(self, choice: int):
        """Handle menu choice selection"""
        if choice == 1:
            self._user_management_menu()
        elif choice == 2:
            self._health_chat_menu()
        elif choice == 3:
            self._food_analysis_menu()
        elif choice == 4:
            self._audio_input_menu()
        elif choice == 5:
            self._cvd_assessment_menu()
        elif choice == 6:
            self._progress_menu()
        elif choice == 7:
            self._report_menu()
        elif choice == 8:
            self._system_status()
        else:
            print("Invalid choice. Please select 1-8.")

    def _user_management_menu(self):
        """User management submenu"""
        print("\n👤 User Management")
        print("-" * 20)

        if not self.current_user:
            name = input("Enter your name: ").strip()
            age_str = input("Enter your age: ").strip()
            email = input("Enter your email (optional): ").strip() or None

            try:
                age = int(age_str)
                user = crud_module.create_user(name, age, email)
                if user:
                    self.current_user = user
                    print(f"✅ User created: {user['name']}")
                else:
                    print("❌ Failed to create user")
            except ValueError:
                print("❌ Invalid age. Please enter a number.")
        else:
            print(f"Current user: {self.current_user['name']}")
            print("1. View profile")
            print("2. Update lifestyle")
            print("3. Switch user")

            choice = input("Choice (1-3): ").strip()
            if choice == "1":
                self._show_user_profile()
            elif choice == "2":
                self._update_lifestyle()
            elif choice == "3":
                self.current_user = None
                print("User logged out")

    def _show_user_profile(self):
        """Display current user profile"""
        if not self.current_user:
            print("No user logged in")
            return

        user_data = crud_module.read_user(self.current_user['id'])
        print(f"\n📋 Profile: {user_data['name']}")
        print(f"Age: {user_data['age']}")
        print(f"Email: {user_data.get('email', 'Not provided')}")
        print(f"CVD Assessments: {len(user_data.get('cvd_scores', []))}")
        print(f"Nutrition Logs: {len(user_data.get('nutrition_logs', []))}")

    def _update_lifestyle(self):
        """Update user lifestyle information"""
        if not self.current_user:
            print("No user logged in")
            return

        print("\n🏃 Update Lifestyle Information")

        try:
            exercise_freq = int(input("Exercise frequency (times per week): ") or "0")
            stress_level = int(input("Stress level (1-10): ") or "5")
            sleep_hours = int(input("Sleep hours per night: ") or "8")
            smoking = input("Do you smoke? (y/n): ").lower().startswith('y')

            lifestyle_update = {
                'profile': {
                    'lifestyle': {
                        'exercise_frequency': exercise_freq,
                        'stress_level': stress_level,
                        'sleep_hours': sleep_hours,
                        'smoking': smoking
                    }
                }
            }

            if crud_module.update_user(self.current_user['id'], lifestyle_update):
                print("✅ Lifestyle information updated")
            else:
                print("❌ Failed to update lifestyle information")

        except ValueError:
            print("❌ Invalid input. Please enter numbers where requested.")

    def _health_chat_menu(self):
        """Health chat submenu"""
        print("\n💬 Health Chat")
        print("-" * 15)

        if not OPENAI_API_KEY:
            print("⚠️ OpenAI API key not configured. Using mock responses.")

        user_input = input("Describe your health concern or question: ").strip()

        if not user_input:
            print("No input provided")
            return

        print("🤖 Processing your question...")

        if OPENAI_API_KEY:
            response = chat_module.get_insights(user_input, self.current_user)
        else:
            # Mock response
            response = {
                'success': True,
                'response': f"Thank you for sharing your concern about '{user_input[:50]}...'. Based on general health guidelines, I recommend consulting with a healthcare provider for personalized advice.",
                'insights': {'risk_factors': [], 'recommendations': []}
            }

        if response['success']:
            print(f"\n🤖 AI Response:")
            print(response['response'])

            insights = response.get('insights', {})
            if insights.get('risk_factors'):
                print(f"\n⚠️ Risk factors identified: {', '.join(insights['risk_factors'])}")
        else:
            print(f"❌ Error: {response.get('error', 'Unknown error')}")

    def _food_analysis_menu(self):
        """Food analysis submenu"""
        print("\n📸 Food Analysis")
        print("-" * 18)

        print("Note: This demo uses mock food analysis.")
        food_description = input("Describe the food you're analyzing: ").strip()

        if not food_description:
            print("No food description provided")
            return

        print("🔍 Analyzing food...")

        # Use mock analysis
        analysis = vision_module.mock_analyze_food(f"mock_{food_description}.jpg")

        if analysis['success']:
            print(f"\n🍽️ Food: {analysis['food_name']}")
            print(f"❤️ Heart Health Score: {analysis['heart_health_score']}/10")

            nutrition = analysis['nutrition']
            print(f"📊 Nutrition (per serving):")
            print(f"   Calories: {nutrition.get('calories', 0)}")
            print(f"   Sodium: {nutrition.get('sodium', 0)}mg")
            print(f"   Fiber: {nutrition.get('fiber', 0)}g")

            cvd_impact = analysis['cvd_impact']
            print(f"⚠️ CVD Risk: {cvd_impact['risk_level']}")

            if self.current_user:
                # Log nutrition
                nutrition_log = {
                    'name': analysis['food_name'],
                    'nutrition': nutrition,
                    'image_path': analysis['image_path']
                }
                crud_module.add_nutrition_log_to_user(self.current_user['id'], nutrition_log)
                print("✅ Nutrition logged to your profile")
        else:
            print(f"❌ Analysis error: {analysis.get('error', 'Unknown error')}")

    def _audio_input_menu(self):
        """Audio input submenu"""
        print("\n🎤 Audio Input")
        print("-" * 15)

        print("Note: This demo uses mock audio transcription.")

        if not OPENAI_API_KEY:
            print("⚠️ OpenAI API key not configured. Using mock transcription.")

        print("🎙️ Simulating audio transcription...")

        # Use mock transcription
        transcript_result = audio_module.mock_transcribe_audio("demo_audio.wav")

        if transcript_result['success']:
            print(f"\n📝 Transcript: {transcript_result['transcript']}")
            print(f"🏥 Health content detected: {transcript_result['contains_health_content']}")

            if transcript_result['contains_health_content']:
                print(f"🔍 Health keywords: {', '.join(transcript_result['health_keywords'])}")

                # Process transcript with health chat
                if OPENAI_API_KEY:
                    chat_response = chat_module.get_insights(
                        {'type': 'audio_transcript', 'content': transcript_result['transcript']},
                        self.current_user
                    )
                    if chat_response['success']:
                        print(f"\n🤖 AI Analysis: {chat_response['response'][:200]}...")
        else:
            print(f"❌ Transcription error: {transcript_result.get('error', 'Unknown error')}")

    def _cvd_assessment_menu(self):
        """CVD assessment submenu"""
        print("\n📊 CVD Risk Assessment")
        print("-" * 25)

        if not self.current_user:
            print("❌ Please log in first to perform CVD assessment")
            return

        print("🔍 Calculating CVD risk...")

        # Get current user data
        user_data = crud_module.read_user(self.current_user['id'])

        # Calculate CVD risk
        cvd_result = cvd_score_module.calculate_cvd_risk(user_data)

        if cvd_result['success']:
            print(f"\n📊 CVD Risk Assessment Results")
            print(f"Risk Score: {cvd_result['percentage']}%")
            print(f"Risk Level: {cvd_result['risk_level']}")

            print(f"\n⚠️ Risk Factors ({len(cvd_result['risk_factors'])}):")
            for factor in cvd_result['risk_factors']:
                print(f"   • {factor}")

            print(f"\n🛡️ Protective Factors ({len(cvd_result['protective_factors'])}):")
            for factor in cvd_result['protective_factors']:
                print(f"   • {factor}")

            print(f"\n💡 Recommendations:")
            for i, rec in enumerate(cvd_result['recommendations'][:3], 1):
                print(f"   {i}. {rec}")

            # Save assessment
            crud_module.add_cvd_score_to_user(self.current_user['id'], cvd_result['score_data'])

            # Update gamification
            gamification_module.log_activity(self.current_user['id'], 'cvd_assessment')
            print("\n✅ Assessment saved and points awarded")

            # Calculate heuristic
            print("\n🧠 Advanced Heuristic Analysis...")
            heuristic_result = heuristic_module.calculate_heuristic(user_data)
            if heuristic_result['success']:
                print(f"Heuristic Score: {heuristic_result['heuristic_score']:.3f}")
                print(f"Risk Category: {heuristic_result['risk_category']}")
        else:
            print(f"❌ Assessment error: {cvd_result.get('error', 'Unknown error')}")

    def _progress_menu(self):
        """Progress and gamification submenu"""
        print("\n🎮 Progress & Achievements")
        print("-" * 28)

        if not self.current_user:
            print("❌ Please log in first to view progress")
            return

        # Get gamification progress
        progress = gamification_module.get_user_progress(self.current_user['id'])

        if progress['success']:
            print(f"🔥 Current Streak: {progress['current_streak']} days")
            print(f"🏆 Longest Streak: {progress['longest_streak']} days")
            print(f"📊 Total Check-ins: {progress['total_checkins']}")
            print(f"⭐ Total Points: {progress['total_points']}")
            print(f"🎯 Level: {progress['level']} ({progress['level_progress']:.1f}%)")

            print(f"\n🏅 Achievements Earned ({progress['achievements_earned']}):")
            for achievement in progress['earned_achievements']:
                print(f"   {achievement['icon']} {achievement['name']}")

            print(f"\n🎯 Available Achievements ({progress['achievements_available']}):")
            for achievement in progress['available_achievements'][:3]:
                print(f"   {achievement['icon']} {achievement['name']} ({achievement['progress']['percentage']:.0f}%)")

            # Daily check-in option
            print(f"\n📅 Daily Check-in")
            if input("Perform daily check-in? (y/n): ").lower().startswith('y'):
                checkin_result = gamification_module.quick_checkin(self.current_user['id'])
                if checkin_result['success']:
                    print(f"✅ Check-in complete! +{checkin_result['points_earned']} points")
                    if checkin_result['new_achievements']:
                        print("🎉 New achievements unlocked!")
        else:
            print(f"❌ Progress error: {progress.get('error', 'Unknown error')}")

    def _report_menu(self):
        """Report generation submenu"""
        print("\n📄 Generate Health Report")
        print("-" * 28)

        if not self.current_user:
            print("❌ Please log in first to generate reports")
            return

        print("Report Types:")
        print("1. 📋 Summary Report")
        print("2. 📊 Comprehensive Report")
        print("3. 📈 Progress Report")

        choice = input("Select report type (1-3): ").strip()

        # Get user data
        user_data = crud_module.read_user(self.current_user['id'])

        report_types = {'1': 'summary', '2': 'comprehensive', '3': 'progress'}
        report_type = report_types.get(choice, 'summary')

        print(f"📄 Generating {report_type} report...")

        pdf_result = pdf_module.export_user_report(user_data, report_type)

        if pdf_result['success']:
            print(f"✅ Report generated successfully!")
            print(f"📁 File: {pdf_result['filename']}")
            print(f"📍 Location: {pdf_result['filepath']}")
            print(f"📊 Size: {pdf_result['file_size']} bytes")
        else:
            print(f"❌ Report generation error: {pdf_result.get('error', 'Unknown error')}")

    def _system_status(self):
        """Display system status and configuration"""
        print("\n🔧 System Status")
        print("-" * 18)

        print(f"OpenAI API: {'✅ Configured' if OPENAI_API_KEY else '❌ Not configured'}")
        print(f"Data Directory: {'✅ Exists' if os.path.exists('data') else '❌ Missing'}")
        print(f"Reports Directory: {'✅ Exists' if os.path.exists('reports') else '❌ Missing'}")

        # Check module availability
        modules = [
            ('CRUD Operations', crud_module),
            ('Chat Module', chat_module),
            ('Audio Module', audio_module),
            ('Vision Module', vision_module),
            ('CVD Score Module', cvd_score_module),
            ('Heuristic Module', heuristic_module),
            ('Gamification Module', gamification_module),
            ('PDF Module', pdf_module)
        ]

        print(f"\n📦 Module Status:")
        for name, module in modules:
            print(f"   {name}: ✅ Loaded")

        # Database status
        users = crud_module.list_users()
        print(f"\n💾 Database Status:")
        print(f"   Total Users: {len(users)}")
        print(f"   Current User: {self.current_user['name'] if self.current_user else 'None'}")

def main():
    """Main entry point"""
    app = CVDApp()

    if len(sys.argv) > 1 and sys.argv[1] == 'demo':
        # Run demo mode
        app.run_demo()
    else:
        # Run interactive mode
        app.run_interactive()

if __name__ == "__main__":
    main()
