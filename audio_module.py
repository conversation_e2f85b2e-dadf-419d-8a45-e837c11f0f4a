"""
Audio Transcription Module for MIT CVD App
Handles audio-to-text conversion using OpenAI Whisper API
"""
import openai
import os
from typing import Dict, Any, Optional
import tempfile

# Optional import for audio processing
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print("Warning: pydub not available. Audio processing will use mock functions.")

from config import OPENAI_API_KEY
from data_models import current_timestamp

# Initialize OpenAI client
if OPENAI_API_KEY:
    try:
        from openai import OpenAI
        client = OpenAI(api_key=OPENAI_API_KEY)
        OPENAI_CLIENT_AVAILABLE = True
    except ImportError:
        openai.api_key = OPENAI_API_KEY
        OPENAI_CLIENT_AVAILABLE = False
else:
    OPENAI_CLIENT_AVAILABLE = False

class AudioProcessor:
    """Audio processing and transcription using Whisper API"""
    
    def __init__(self):
        self.supported_formats = ['.mp3', '.mp4', '.mpeg', '.mpga', '.m4a', '.wav', '.webm']
        self.max_file_size = 25 * 1024 * 1024  # 25MB limit for Whisper API
    
    def transcribe_audio(self, file_path: str, language: str = None) -> Dict[str, Any]:
        """
        Transcribe audio file to text using OpenAI Whisper
        
        Args:
            file_path: Path to audio file
            language: Optional language code (e.g., 'en', 'es')
            
        Returns:
            Dict with transcription result and metadata
        """
        try:
            # Validate file
            validation_result = self._validate_audio_file(file_path)
            if not validation_result['valid']:
                return self._create_error_response(validation_result['error'])
            
            # Process file if needed (convert format, compress, etc.)
            processed_file_path = self._prepare_audio_file(file_path)
            
            # Transcribe using Whisper API
            with open(processed_file_path, 'rb') as audio_file:
                transcript_params = {
                    'file': audio_file,
                    'model': 'whisper-1',
                    'response_format': 'verbose_json'
                }
                
                if language:
                    transcript_params['language'] = language
                
                if OPENAI_CLIENT_AVAILABLE:
                    transcript = client.audio.transcriptions.create(**transcript_params)
                else:
                    transcript = openai.Audio.transcribe(**transcript_params)
            
            # Clean up temporary file if created
            if processed_file_path != file_path:
                os.remove(processed_file_path)
            
            return {
                'success': True,
                'transcript': transcript.text,
                'language': transcript.language if hasattr(transcript, 'language') else 'unknown',
                'duration': transcript.duration if hasattr(transcript, 'duration') else None,
                'segments': transcript.segments if hasattr(transcript, 'segments') else [],
                'confidence': self._calculate_confidence(transcript),
                'original_file': file_path,
                'timestamp': current_timestamp()
            }
            
        except Exception as e:
            return self._create_error_response(f"Transcription error: {str(e)}")
    
    def transcribe_audio_for_health_chat(self, file_path: str) -> Dict[str, Any]:
        """
        Transcribe audio specifically for health conversations
        Includes health-specific processing and validation
        """
        result = self.transcribe_audio(file_path)
        
        if result['success']:
            # Add health-specific processing
            transcript = result['transcript']
            
            # Basic health keyword detection
            health_keywords = self._detect_health_keywords(transcript)
            
            result.update({
                'health_keywords': health_keywords,
                'contains_health_content': len(health_keywords) > 0,
                'processed_for_health': True
            })
        
        return result
    
    def _validate_audio_file(self, file_path: str) -> Dict[str, Any]:
        """Validate audio file for processing"""
        if not os.path.exists(file_path):
            return {'valid': False, 'error': f"File not found: {file_path}"}
        
        # Check file extension
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_formats:
            return {'valid': False, 'error': f"Unsupported format: {file_ext}"}
        
        # Check file size
        file_size = os.path.getsize(file_path)
        if file_size > self.max_file_size:
            return {'valid': False, 'error': f"File too large: {file_size} bytes (max: {self.max_file_size})"}
        
        if file_size == 0:
            return {'valid': False, 'error': "Empty file"}
        
        return {'valid': True}
    
    def _prepare_audio_file(self, file_path: str) -> str:
        """
        Prepare audio file for Whisper API
        Convert format or compress if needed
        """
        if not PYDUB_AVAILABLE:
            return file_path  # Return original file if pydub not available

        file_ext = os.path.splitext(file_path)[1].lower()
        file_size = os.path.getsize(file_path)

        # If file is already in good format and size, return as-is
        if file_ext in ['.mp3', '.wav'] and file_size < self.max_file_size * 0.8:
            return file_path

        try:
            # Load audio file
            audio = AudioSegment.from_file(file_path)

            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
            temp_path = temp_file.name
            temp_file.close()

            # Export as MP3 with compression if needed
            if file_size > self.max_file_size * 0.8:
                # Reduce bitrate for compression
                audio.export(temp_path, format="mp3", bitrate="64k")
            else:
                # Standard quality
                audio.export(temp_path, format="mp3", bitrate="128k")

            return temp_path

        except Exception as e:
            print(f"Warning: Could not process audio file {file_path}: {e}")
            return file_path  # Return original file and hope for the best
    
    def _calculate_confidence(self, transcript) -> float:
        """Calculate overall confidence score from transcript segments"""
        if not hasattr(transcript, 'segments') or not transcript.segments:
            return 0.8  # Default confidence
        
        # Average confidence from segments (if available)
        confidences = []
        for segment in transcript.segments:
            if hasattr(segment, 'avg_logprob'):
                # Convert log probability to confidence (rough approximation)
                confidence = min(1.0, max(0.0, (segment.avg_logprob + 1.0)))
                confidences.append(confidence)
        
        return sum(confidences) / len(confidences) if confidences else 0.8
    
    def _detect_health_keywords(self, transcript: str) -> list:
        """Detect health-related keywords in transcript"""
        health_keywords = [
            'chest pain', 'shortness of breath', 'heart rate', 'blood pressure',
            'cholesterol', 'diabetes', 'exercise', 'diet', 'stress', 'sleep',
            'medication', 'symptoms', 'fatigue', 'dizziness', 'palpitations',
            'family history', 'smoking', 'alcohol', 'weight', 'nutrition'
        ]
        
        transcript_lower = transcript.lower()
        found_keywords = []
        
        for keyword in health_keywords:
            if keyword in transcript_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            'success': False,
            'error': error_message,
            'transcript': '',
            'timestamp': current_timestamp()
        }

# Convenience functions
def transcribe_audio(file_path: str, language: str = None) -> Dict[str, Any]:
    """Main transcription function - wrapper for the class method"""
    processor = AudioProcessor()
    return processor.transcribe_audio(file_path, language)

def transcribe_health_audio(file_path: str) -> Dict[str, Any]:
    """Transcribe audio specifically for health conversations"""
    processor = AudioProcessor()
    return processor.transcribe_audio_for_health_chat(file_path)

def quick_voice_note(file_path: str) -> str:
    """Quick transcription that returns just the text"""
    result = transcribe_audio(file_path)
    return result.get('transcript', '') if result.get('success') else ''

# Mock function for demo purposes (if OpenAI API is not available)
def mock_transcribe_audio(file_path: str) -> Dict[str, Any]:
    """Mock transcription for demo purposes"""
    return {
        'success': True,
        'transcript': "I've been feeling some chest tightness lately, especially after climbing stairs. I'm also concerned about my family history of heart disease.",
        'language': 'en',
        'duration': 15.5,
        'confidence': 0.95,
        'health_keywords': ['chest tightness', 'family history', 'heart disease'],
        'contains_health_content': True,
        'original_file': file_path,
        'timestamp': current_timestamp(),
        'mock': True
    }
